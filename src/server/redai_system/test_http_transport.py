#!/usr/bin/env python3
"""
Test script để kiểm tra Streamable HTTP Transport của Shipment Server

Script này sẽ:
1. Khởi động server trong background
2. <PERSON><PERSON><PERSON> tra các endpoints
3. Test kết nối MCP
4. <PERSON><PERSON><PERSON> dẹp và thoát

Sử dụng:
    python test_http_transport.py
"""

import asyncio
import json
import time
import subprocess
import sys
import os
import signal
import httpx
from pathlib import Path

# Cấu hình test
TEST_HOST = "127.0.0.1"
TEST_PORT = 8000
TEST_PATH = "/mcp"
BASE_URL = f"http://{TEST_HOST}:{TEST_PORT}"
MCP_URL = f"{BASE_URL}{TEST_PATH}"

class ServerTester:
    def __init__(self):
        self.server_process = None
        self.client = httpx.AsyncClient(timeout=30.0)
        
    async def start_server(self):
        """Khởi động server trong background"""
        print("🚀 Đang khởi động Shipment Server...")
        
        # Thiết lập environment variables
        env = os.environ.copy()
        env.update({
            "GHN_HTTP_HOST": TEST_HOST,
            "GHN_HTTP_PORT": str(TEST_PORT),
            "GHN_HTTP_PATH": TEST_PATH,
            # Test credentials
            "GHN_TOKEN": "test_token_12345",
            "GHN_SHOP_ID": "12345",
            "GHN_ENVIRONMENT": "test",
            "GHTK_TOKEN": "test_ghtk_token_12345",
            "GHTK_PARTNER_CODE": "test_partner_12345",
            "GHTK_ENVIRONMENT": "test",
            "JT_USERNAME": "test_jt_username",
            "JT_API_KEY": "test_jt_api_key_12345",
            "JT_CUSTOMER_CODE": "test_customer_12345",
            "JT_ENVIRONMENT": "test",
            "AHAMOVE_API_KEY": "test_ahamove_api_key_12345",
            "AHAMOVE_TOKEN": "test_ahamove_token_12345",
            "AHAMOVE_MOBILE": "84912345678",
            "AHAMOVE_ENVIRONMENT": "test"
        })
        
        # Khởi động server
        server_script = Path(__file__).parent / "shipment_server.py"
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Đợi server khởi động
        print("⏳ Đợi server khởi động...")
        await asyncio.sleep(5)
        
        # Kiểm tra server có chạy không
        if self.server_process.poll() is not None:
            stdout, stderr = self.server_process.communicate()
            print(f"❌ Server không khởi động được!")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
        print("✅ Server đã khởi động thành công!")
        return True
    
    async def test_health_check(self):
        """Test health check endpoint"""
        print("\n🔍 Testing Health Check...")
        try:
            response = await self.client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("✅ Health check: OK")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    async def test_docs_endpoint(self):
        """Test API documentation endpoint"""
        print("\n📚 Testing API Documentation...")
        try:
            response = await self.client.get(f"{BASE_URL}/docs")
            if response.status_code == 200:
                print("✅ API docs: Accessible")
                return True
            else:
                print(f"❌ API docs failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API docs error: {e}")
            return False
    
    async def test_mcp_endpoint(self):
        """Test MCP endpoint với initialize request"""
        print("\n🔌 Testing MCP Endpoint...")
        try:
            # Test MCP initialize request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await self.client.post(
                MCP_URL,
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ MCP endpoint: Responsive")
                print(f"   Server info: {result.get('result', {}).get('serverInfo', {})}")
                return True
            else:
                print(f"❌ MCP endpoint failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ MCP endpoint error: {e}")
            return False
    
    async def test_tools_list(self):
        """Test lấy danh sách tools"""
        print("\n🔧 Testing Tools List...")
        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self.client.post(
                MCP_URL,
                json=tools_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                tools = result.get('result', {}).get('tools', [])
                print(f"✅ Tools list: {len(tools)} tools available")
                
                # Hiển thị một số tools
                for i, tool in enumerate(tools[:5]):
                    print(f"   • {tool.get('name', 'Unknown')}")
                if len(tools) > 5:
                    print(f"   ... và {len(tools) - 5} tools khác")
                    
                return True
            else:
                print(f"❌ Tools list failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Tools list error: {e}")
            return False
    
    async def test_resources_list(self):
        """Test lấy danh sách resources"""
        print("\n📚 Testing Resources List...")
        try:
            resources_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/list"
            }
            
            response = await self.client.post(
                MCP_URL,
                json=resources_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                resources = result.get('result', {}).get('resources', [])
                print(f"✅ Resources list: {len(resources)} resources available")
                
                # Hiển thị một số resources
                for i, resource in enumerate(resources[:5]):
                    print(f"   • {resource.get('uri', 'Unknown')}")
                if len(resources) > 5:
                    print(f"   ... và {len(resources) - 5} resources khác")
                    
                return True
            else:
                print(f"❌ Resources list failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Resources list error: {e}")
            return False
    
    async def cleanup(self):
        """Dọn dẹp và tắt server"""
        print("\n🧹 Cleaning up...")
        
        await self.client.aclose()
        
        if self.server_process:
            print("⏹️  Đang tắt server...")
            self.server_process.terminate()
            
            # Đợi server tắt
            try:
                self.server_process.wait(timeout=5)
                print("✅ Server đã tắt thành công")
            except subprocess.TimeoutExpired:
                print("⚠️  Server không tắt, force kill...")
                self.server_process.kill()
                self.server_process.wait()
    
    async def run_all_tests(self):
        """Chạy tất cả tests"""
        print("="*60)
        print("🧪 SHIPMENT SERVER HTTP TRANSPORT TEST")
        print("="*60)
        
        # Khởi động server
        if not await self.start_server():
            return False
        
        try:
            # Chạy các tests
            tests = [
                self.test_health_check,
                self.test_docs_endpoint,
                self.test_mcp_endpoint,
                self.test_tools_list,
                self.test_resources_list
            ]
            
            results = []
            for test in tests:
                result = await test()
                results.append(result)
            
            # Tổng kết
            print("\n" + "="*60)
            print("📊 KẾT QUẢ TEST")
            print("="*60)
            
            passed = sum(results)
            total = len(results)
            
            print(f"✅ Passed: {passed}/{total}")
            print(f"❌ Failed: {total - passed}/{total}")
            
            if passed == total:
                print("\n🎉 TẤT CẢ TESTS ĐỀU THÀNH CÔNG!")
                print(f"🌐 Server URL: {BASE_URL}")
                print(f"📡 MCP Endpoint: {MCP_URL}")
                print(f"📋 API Docs: {BASE_URL}/docs")
            else:
                print("\n⚠️  MỘT SỐ TESTS THẤT BẠI!")
                
            return passed == total
            
        finally:
            await self.cleanup()

async def main():
    """Main function"""
    tester = ServerTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ Chuyển đổi sang Streamable HTTP Transport thành công!")
    else:
        print("\n❌ Có lỗi trong quá trình test!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
