#!/usr/bin/env python3
"""
Simple test script cho Streamable HTTP Transport
Không sử dụng emoji để tránh lỗi encoding trên Windows
"""

import asyncio
import json
import time
import subprocess
import sys
import os
import httpx
from pathlib import Path

# C<PERSON><PERSON>nh test
TEST_HOST = "127.0.0.1"
TEST_PORT = 8000
TEST_PATH = "/mcp"
BASE_URL = f"http://{TEST_HOST}:{TEST_PORT}"
MCP_URL = f"{BASE_URL}{TEST_PATH}"

async def test_server():
    """Test server đơn giản"""
    print("=" * 60)
    print("SHIPMENT SERVER HTTP TRANSPORT TEST")
    print("=" * 60)
    
    # Thiết lập environment variables
    env = os.environ.copy()
    env.update({
        "GHN_HTTP_HOST": TEST_HOST,
        "GHN_HTTP_PORT": str(TEST_PORT),
        "GHN_HTTP_PATH": TEST_PATH,
        # Test credentials
        "GHN_TOKEN": "test_token_12345",
        "GHN_SHOP_ID": "12345",
        "GHN_ENVIRONMENT": "test",
        "GHTK_TOKEN": "test_ghtk_token_12345",
        "GHTK_PARTNER_CODE": "test_partner_12345",
        "GHTK_ENVIRONMENT": "test",
        "JT_USERNAME": "test_jt_username",
        "JT_API_KEY": "test_jt_api_key_12345",
        "JT_CUSTOMER_CODE": "test_customer_12345",
        "JT_ENVIRONMENT": "test",
        "AHAMOVE_API_KEY": "test_ahamove_api_key_12345",
        "AHAMOVE_TOKEN": "test_ahamove_token_12345",
        "AHAMOVE_MOBILE": "84912345678",
        "AHAMOVE_ENVIRONMENT": "test"
    })
    
    print("Dang khoi dong Shipment Server...")
    
    # Khởi động server
    server_script = Path(__file__).parent / "shipment_server.py"
    server_process = subprocess.Popen(
        [sys.executable, str(server_script)],
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Đợi server khởi động
    print("Doi server khoi dong...")
    await asyncio.sleep(8)
    
    # Kiểm tra server có chạy không
    if server_process.poll() is not None:
        stdout, stderr = server_process.communicate()
        print("Server khong khoi dong duoc!")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return False
    
    print("Server da khoi dong thanh cong!")
    
    # Test các endpoints
    client = httpx.AsyncClient(timeout=30.0)
    
    try:
        # Test 1: Health check
        print("\nTesting Health Check...")
        try:
            response = await client.get(f"{BASE_URL}/health")
            if response.status_code == 200:
                print("Health check: OK")
            else:
                print(f"Health check failed: {response.status_code}")
        except Exception as e:
            print(f"Health check error: {e}")
        
        # Test 2: API docs
        print("\nTesting API Documentation...")
        try:
            response = await client.get(f"{BASE_URL}/docs")
            if response.status_code == 200:
                print("API docs: Accessible")
            else:
                print(f"API docs failed: {response.status_code}")
        except Exception as e:
            print(f"API docs error: {e}")
        
        # Test 3: MCP endpoint
        print("\nTesting MCP Endpoint...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await client.post(
                MCP_URL,
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("MCP endpoint: Responsive")
                server_info = result.get('result', {}).get('serverInfo', {})
                if server_info:
                    print(f"Server info: {server_info}")
            else:
                print(f"MCP endpoint failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"MCP endpoint error: {e}")
        
        # Test 4: Tools list
        print("\nTesting Tools List...")
        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await client.post(
                MCP_URL,
                json=tools_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                tools = result.get('result', {}).get('tools', [])
                print(f"Tools list: {len(tools)} tools available")
                
                # Hiển thị một số tools
                for i, tool in enumerate(tools[:3]):
                    print(f"  - {tool.get('name', 'Unknown')}")
                if len(tools) > 3:
                    print(f"  ... va {len(tools) - 3} tools khac")
            else:
                print(f"Tools list failed: {response.status_code}")
                
        except Exception as e:
            print(f"Tools list error: {e}")
        
        # Test 5: Resources list
        print("\nTesting Resources List...")
        try:
            resources_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/list"
            }
            
            response = await client.post(
                MCP_URL,
                json=resources_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                resources = result.get('result', {}).get('resources', [])
                print(f"Resources list: {len(resources)} resources available")
                
                # Hiển thị một số resources
                for i, resource in enumerate(resources[:3]):
                    print(f"  - {resource.get('uri', 'Unknown')}")
                if len(resources) > 3:
                    print(f"  ... va {len(resources) - 3} resources khac")
            else:
                print(f"Resources list failed: {response.status_code}")
                
        except Exception as e:
            print(f"Resources list error: {e}")
        
        print("\n" + "=" * 60)
        print("KET QUA TEST")
        print("=" * 60)
        print("Chuyen doi sang Streamable HTTP Transport thanh cong!")
        print(f"Server URL: {BASE_URL}")
        print(f"MCP Endpoint: {MCP_URL}")
        print(f"API Docs: {BASE_URL}/docs")
        
    finally:
        await client.aclose()
        
        # Tắt server
        print("\nDang tat server...")
        server_process.terminate()
        
        try:
            server_process.wait(timeout=5)
            print("Server da tat thanh cong")
        except subprocess.TimeoutExpired:
            print("Server khong tat, force kill...")
            server_process.kill()
            server_process.wait()

if __name__ == "__main__":
    asyncio.run(test_server())
