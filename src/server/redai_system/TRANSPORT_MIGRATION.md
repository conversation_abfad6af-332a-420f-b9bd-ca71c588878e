# Hướng dẫn chuyển đổi từ SSE sang Streamable HTTP Transport

## Tổng quan

File `shipment_server.py` đã được chuyển đổi từ **SSE (Server-Sent Events)** transport sang **Streamable HTTP** transport để cải thiện hiệu suất và tính tương thích.

## Những thay đổi chính

### 1. <PERSON><PERSON><PERSON> hình Server

**Trước (SSE):**
```python
# Cấu hình SSE server
SSE_HOST = os.getenv("GHN_SSE_HOST", "127.0.0.1")
SSE_PORT = int(os.getenv("GHN_SSE_PORT", "8000"))

# Chạy server
mcp.run(transport="sse")
```

**Sau (Streamable HTTP):**
```python
# Cấu hình HTTP server
HTTP_HOST = os.getenv("GHN_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("GHN_HTTP_PORT", "8000"))
HTTP_PATH = os.getenv("GHN_HTTP_PATH", "/mcp")

# Chạy server
mcp.run(
    transport="streamable-http",
    host=HTTP_HOST,
    port=HTTP_PORT,
    path=HTTP_PATH,
    log_level="info"
)
```

### 2. Biến môi trường

**Biến cũ (SSE):**
- `GHN_SSE_HOST` → `GHN_HTTP_HOST`
- `GHN_SSE_PORT` → `GHN_HTTP_PORT`
- Thêm mới: `GHN_HTTP_PATH`

### 3. Endpoints

**Trước (SSE):**
- SSE Endpoint: `http://127.0.0.1:8000/sse`
- Messages Endpoint: `http://127.0.0.1:8000/messages`

**Sau (Streamable HTTP):**
- MCP Endpoint: `http://127.0.0.1:8000/mcp`
- API Documentation: `http://127.0.0.1:8000/docs`

## Cách sử dụng

### 1. Chạy server trực tiếp

```bash
python src/server/redai_system/shipment_server.py
```

### 2. Sử dụng FastMCP CLI

```bash
# Chạy với cấu hình mặc định
fastmcp run src/server/redai_system/shipment_server.py

# Chạy với cấu hình tùy chỉnh
fastmcp run src/server/redai_system/shipment_server.py --transport streamable-http --port 8001
```

### 3. Cấu hình client

**Python Client:**
```python
from fastmcp import FastMCPClient

# Kết nối đến server
client = FastMCPClient("http://127.0.0.1:8000/mcp")
```

**JSON Config:**
```json
{
  "mcpServers": {
    "shipment-server": {
      "url": "http://127.0.0.1:8000/mcp",
      "transport": "streamable-http"
    }
  }
}
```

## Lợi ích của Streamable HTTP

1. **Hiệu suất tốt hơn**: Streamable HTTP được tối ưu hóa cho web deployments
2. **Tương thích rộng**: Hoạt động tốt với các reverse proxy và load balancers
3. **Dễ debug**: Có thể test endpoints bằng curl hoặc Postman
4. **API Documentation**: Tự động tạo docs tại `/docs`
5. **Khuyến nghị**: FastMCP khuyến nghị sử dụng cho production

## Kiểm tra hoạt động

### 1. Health Check
```bash
curl http://127.0.0.1:8000/health
```

### 2. API Documentation
Truy cập: http://127.0.0.1:8000/docs

### 3. MCP Endpoint
```bash
curl -X POST http://127.0.0.1:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc": "2.0", "method": "initialize", "id": 1}'
```

## Troubleshooting

### Lỗi port đã được sử dụng
```bash
# Kiểm tra port đang sử dụng
netstat -ano | findstr :8000

# Thay đổi port
set GHN_HTTP_PORT=8001
python src/server/redai_system/shipment_server.py
```

### Lỗi kết nối
- Kiểm tra firewall
- Đảm bảo server đang chạy
- Kiểm tra URL endpoint chính xác

## Tương thích ngược

- File cấu hình cũ vẫn hoạt động với stdio transport
- SSE transport vẫn được hỗ trợ nhưng deprecated
- Khuyến nghị migrate sang streamable-http cho tất cả deployments mới
