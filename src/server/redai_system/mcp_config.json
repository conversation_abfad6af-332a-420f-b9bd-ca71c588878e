{"mcpServers": {"ghn-shipment-stdio": {"command": "python", "args": ["src/server/redai_system/shipment_server.py"], "env": {"GHN_TOKEN": "your_ghn_token_here", "GHN_SHOP_ID": "your_shop_id_here", "GHN_ENVIRONMENT": "test"}, "transport": "stdio", "description": "GHN Shipment MCP Server - Stdio Transport"}, "ghn-shipment-http": {"url": "http://localhost:8000/mcp", "env": {"GHN_TOKEN": "your_ghn_token_here", "GHN_SHOP_ID": "your_shop_id_here", "GHN_ENVIRONMENT": "test", "GHN_HTTP_HOST": "127.0.0.1", "GHN_HTTP_PORT": "8000", "GHN_HTTP_PATH": "/mcp"}, "transport": "streamable-http", "description": "GHN Shipment MCP Server - Streamable HTTP Transport"}, "ghn-shipment-http-custom": {"url": "http://localhost:8001/mcp", "env": {"GHN_TOKEN": "your_ghn_token_here", "GHN_SHOP_ID": "your_shop_id_here", "GHN_ENVIRONMENT": "test", "GHN_HTTP_HOST": "127.0.0.1", "GHN_HTTP_PORT": "8001", "GHN_HTTP_PATH": "/mcp"}, "transport": "streamable-http", "description": "GHN Shipment MCP Server - Streamable HTTP Custom Port"}}}