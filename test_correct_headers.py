#!/usr/bin/env python3
"""
Test với correct headers cho Streamable HTTP
"""

import asyncio
import httpx
import json

def parse_sse_response(content):
    """Parse Server-Sent Events response"""
    lines = content.split('\n')
    data_lines = []
    
    for line in lines:
        if line.startswith('data: '):
            data_lines.append(line[6:])  # Remove 'data: ' prefix
    
    if data_lines:
        try:
            return json.loads(''.join(data_lines))
        except:
            return None
    return None

async def test_with_correct_headers():
    """Test với correct headers"""
    print("=" * 60)
    print("TEST STREAMABLE HTTP VỚI CORRECT HEADERS")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Headers đúng cho Streamable HTTP
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream"
    }
    
    async with httpx.AsyncClient(timeout=15.0) as client:
        
        # Test 1: Initialize
        print("\n1. Testing MCP Initialize...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await client.post(
                f"{base_url}/mcp/",  # Note the trailing slash
                json=mcp_request,
                headers=headers
            )
            
            print(f"   Status: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
            
            if response.status_code == 200:
                # Parse SSE response
                json_data = parse_sse_response(response.text)
                if json_data and json_data.get('result'):
                    print("   ✅ Initialize successful!")
                    server_info = json_data['result'].get('serverInfo', {})
                    print(f"   Server: {server_info.get('name', 'Unknown')}")
                    print(f"   Version: {server_info.get('version', 'Unknown')}")
                    
                    # Test 2: Tools list
                    print("\n2. Testing Tools List...")
                    tools_request = {
                        "jsonrpc": "2.0",
                        "id": 2,
                        "method": "tools/list"
                    }
                    
                    response = await client.post(
                        f"{base_url}/mcp/",
                        json=tools_request,
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        json_data = parse_sse_response(response.text)
                        if json_data and json_data.get('result'):
                            tools = json_data['result'].get('tools', [])
                            print(f"   ✅ Found {len(tools)} tools")
                            
                            # Show some tools
                            print("   Sample tools:")
                            for i, tool in enumerate(tools[:5]):
                                print(f"     • {tool.get('name', 'Unknown')}")
                            if len(tools) > 5:
                                print(f"     ... and {len(tools) - 5} more")
                    
                    # Test 3: Resources list
                    print("\n3. Testing Resources List...")
                    resources_request = {
                        "jsonrpc": "2.0",
                        "id": 3,
                        "method": "resources/list"
                    }
                    
                    response = await client.post(
                        f"{base_url}/mcp/",
                        json=resources_request,
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        json_data = parse_sse_response(response.text)
                        if json_data and json_data.get('result'):
                            resources = json_data['result'].get('resources', [])
                            print(f"   ✅ Found {len(resources)} resources")
                            
                            # Show some resources
                            print("   Sample resources:")
                            for i, resource in enumerate(resources[:5]):
                                print(f"     • {resource.get('uri', 'Unknown')}")
                            if len(resources) > 5:
                                print(f"     ... and {len(resources) - 5} more")
                
                else:
                    print("   ❌ Failed to parse response")
                    print(f"   Raw response: {response.text[:200]}...")
            else:
                print(f"   ❌ Request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 KẾT QUẢ")
    print("=" * 60)
    print("✅ Server hoạt động với Streamable HTTP Transport")
    print("📡 Endpoint: http://127.0.0.1:8000/mcp/")
    print("🔧 Headers cần thiết:")
    print("   Content-Type: application/json")
    print("   Accept: application/json, text/event-stream")

if __name__ == "__main__":
    asyncio.run(test_with_correct_headers())
