#!/usr/bin/env python3
"""
Debug response content
"""

import asyncio
import httpx

async def debug_response():
    """Debug response"""
    print("Debugging response content...")
    
    base_url = "http://127.0.0.1:8000"
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json, text/event-stream"
    }
    
    async with httpx.AsyncClient(timeout=10.0, follow_redirects=True) as client:
        
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await client.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers=headers
            )
            
            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")
            print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"Content Length: {len(response.content)}")
            print(f"Raw Content: {response.content[:200]}...")
            print(f"Text Content: {response.text[:200]}...")
            
            # Try to parse as JSON
            try:
                json_data = response.json()
                print(f"JSON Data: {json_data}")
            except Exception as e:
                print(f"JSON Parse Error: {e}")
                
        except Exception as e:
            print(f"Request Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_response())
