#!/usr/bin/env python3
"""
Test endpoints của Streamable HTTP server
"""

import asyncio
import httpx
import json

async def test_endpoints():
    """Test các endpoints của server"""
    print("=" * 60)
    print("TESTING STREAMABLE HTTP ENDPOINTS")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        
        # Test 1: API Documentation
        print("\n1. Testing API Documentation...")
        try:
            response = await client.get(f"{base_url}/docs")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ API docs accessible")
            else:
                print(f"   ❌ API docs failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 2: MCP Endpoint - Initialize
        print("\n2. Testing MCP Initialize...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await client.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print("   ✅ MCP initialize successful")
                server_info = result.get('result', {}).get('serverInfo', {})
                if server_info:
                    print(f"   Server: {server_info.get('name', 'Unknown')}")
                    print(f"   Version: {server_info.get('version', 'Unknown')}")
            else:
                print(f"   ❌ MCP initialize failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 3: Tools List
        print("\n3. Testing Tools List...")
        try:
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await client.post(
                f"{base_url}/mcp",
                json=tools_request,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                tools = result.get('result', {}).get('tools', [])
                print(f"   ✅ Found {len(tools)} tools")
                
                # Hiển thị một số tools
                print("   Sample tools:")
                for i, tool in enumerate(tools[:5]):
                    print(f"     • {tool.get('name', 'Unknown')}")
                if len(tools) > 5:
                    print(f"     ... and {len(tools) - 5} more")
            else:
                print(f"   ❌ Tools list failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 4: Resources List
        print("\n4. Testing Resources List...")
        try:
            resources_request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "resources/list"
            }
            
            response = await client.post(
                f"{base_url}/mcp",
                json=resources_request,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                resources = result.get('result', {}).get('resources', [])
                print(f"   ✅ Found {len(resources)} resources")
                
                # Hiển thị một số resources
                print("   Sample resources:")
                for i, resource in enumerate(resources[:5]):
                    print(f"     • {resource.get('uri', 'Unknown')}")
                if len(resources) > 5:
                    print(f"     ... and {len(resources) - 5} more")
            else:
                print(f"   ❌ Resources list failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 5: Health Check (nếu có)
        print("\n5. Testing Health Check...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Health check OK")
            elif response.status_code == 404:
                print("   ⚠️  Health endpoint not available (normal)")
            else:
                print(f"   ❌ Health check failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print("✅ Server is running with Streamable HTTP transport")
    print("🌐 Server URL: http://127.0.0.1:8000")
    print("📡 MCP Endpoint: http://127.0.0.1:8000/mcp")
    print("📋 API Docs: http://127.0.0.1:8000/docs")
    print("\n🎉 Chuyen doi tu SSE sang Streamable HTTP THANH CONG!")

if __name__ == "__main__":
    asyncio.run(test_endpoints())
