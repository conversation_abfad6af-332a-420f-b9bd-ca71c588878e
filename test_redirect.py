#!/usr/bin/env python3
"""
Test redirect behavior của server
"""

import asyncio
import httpx

async def test_redirects():
    """Test redirect behavior"""
    print("Testing redirect behavior...")
    
    base_url = "http://127.0.0.1:8000"
    
    async with httpx.AsyncClient(timeout=10.0, follow_redirects=True) as client:
        
        # Test MCP endpoint với follow redirects
        print("\nTesting MCP with redirects...")
        try:
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await client.post(
                f"{base_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            print(f"Final Status: {response.status_code}")
            print(f"Final URL: {response.url}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ MCP initialize successful after redirect")
                server_info = result.get('result', {}).get('serverInfo', {})
                if server_info:
                    print(f"Server: {server_info.get('name', 'Unknown')}")
                    print(f"Version: {server_info.get('version', 'Unknown')}")
                    
                # Test tools list
                print("\nTesting tools list...")
                tools_request = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/list"
                }
                
                response = await client.post(
                    response.url,  # Use the final URL
                    json=tools_request,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    tools = result.get('result', {}).get('tools', [])
                    print(f"✅ Found {len(tools)} tools")
                    
                    # Show some tools
                    for i, tool in enumerate(tools[:3]):
                        print(f"  • {tool.get('name', 'Unknown')}")
                    if len(tools) > 3:
                        print(f"  ... and {len(tools) - 3} more")
                else:
                    print(f"❌ Tools list failed: {response.status_code}")
            else:
                print(f"❌ MCP failed: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_redirects())
